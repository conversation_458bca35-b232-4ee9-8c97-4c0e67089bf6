/**
 * Big River Coffee - Tailwind v4 Configuration
 * Outdoor Adventure Theme with Modern UI
 */

@import 'tailwindcss';

@theme {
  /* Brand Colors - Big River Coffee */

  /* Primary Green - #3A5C5C (Darker Green/Teal) */
  --color-primary-50: oklch(0.97 0.02 180);
  --color-primary-100: oklch(0.94 0.04 180);
  --color-primary-200: oklch(0.88 0.08 180);
  --color-primary-300: oklch(0.80 0.12 180);
  --color-primary-400: oklch(0.70 0.16 180);
  --color-primary-500: oklch(0.58 0.18 180);
  --color-primary-600: oklch(0.46 0.16 180);
  --color-primary-700: oklch(0.36 0.14 180);
  --color-primary-800: oklch(0.28 0.12 180);
  --color-primary-900: oklch(0.20 0.10 180);
  --color-primary-950: oklch(0.14 0.08 180);

  /* Orange - #db8027 (Brand Orange) */
  --color-orange-50: oklch(0.98 0.02 65);
  --color-orange-100: oklch(0.95 0.05 65);
  --color-orange-200: oklch(0.90 0.10 65);
  --color-orange-300: oklch(0.84 0.15 65);
  --color-orange-400: oklch(0.76 0.20 65);
  --color-orange-500: oklch(0.68 0.25 65);
  --color-orange-600: oklch(0.60 0.22 65);
  --color-orange-700: oklch(0.50 0.18 65);
  --color-orange-800: oklch(0.40 0.15 65);
  --color-orange-900: oklch(0.30 0.12 65);
  --color-orange-950: oklch(0.20 0.10 65);

  /* Tan/Cream - #EEEDC1 (Light Neutral) */
  --color-tan-50: oklch(0.99 0.01 85);
  --color-tan-100: oklch(0.96 0.02 85);
  --color-tan-200: oklch(0.92 0.04 85);
  --color-tan-300: oklch(0.88 0.06 85);
  --color-tan-400: oklch(0.82 0.08 85);
  --color-tan-500: oklch(0.76 0.10 85);
  --color-tan-600: oklch(0.68 0.08 85);
  --color-tan-700: oklch(0.58 0.06 85);
  --color-tan-800: oklch(0.48 0.04 85);
  --color-tan-900: oklch(0.38 0.02 85);
  --color-tan-950: oklch(0.28 0.01 85);

  /* Red/Brown - #b25538 (Accent Red) */
  --color-red-50: oklch(0.97 0.02 35);
  --color-red-100: oklch(0.94 0.04 35);
  --color-red-200: oklch(0.88 0.08 35);
  --color-red-300: oklch(0.80 0.12 35);
  --color-red-400: oklch(0.70 0.16 35);
  --color-red-500: oklch(0.60 0.18 35);
  --color-red-600: oklch(0.50 0.16 35);
  --color-red-700: oklch(0.42 0.14 35);
  --color-red-800: oklch(0.34 0.12 35);
  --color-red-900: oklch(0.26 0.10 35);
  --color-red-950: oklch(0.18 0.08 35);

  /* Legacy Color Mappings for Compatibility */
  --color-army-50: var(--color-primary-50);
  --color-army-100: var(--color-primary-100);
  --color-army-200: var(--color-primary-200);
  --color-army-300: var(--color-primary-300);
  --color-army-400: var(--color-primary-400);
  --color-army-500: var(--color-primary-500);
  --color-army-600: var(--color-primary-600);
  --color-army-700: var(--color-primary-700);
  --color-army-800: var(--color-primary-800);
  --color-army-900: var(--color-primary-900);
  --color-army-950: var(--color-primary-950);

  --color-amber-50: var(--color-orange-50);
  --color-amber-100: var(--color-orange-100);
  --color-amber-200: var(--color-orange-200);
  --color-amber-300: var(--color-orange-300);
  --color-amber-400: var(--color-orange-400);
  --color-amber-500: var(--color-orange-500);
  --color-amber-600: var(--color-orange-600);
  --color-amber-700: var(--color-orange-700);
  --color-amber-800: var(--color-orange-800);
  --color-amber-900: var(--color-orange-900);
  --color-amber-950: var(--color-orange-950);

  /* Neutral Grays */
  --color-neutral-50: oklch(0.98 0 0);
  --color-neutral-100: oklch(0.95 0 0);
  --color-neutral-200: oklch(0.90 0 0);
  --color-neutral-300: oklch(0.83 0 0);
  --color-neutral-400: oklch(0.64 0 0);
  --color-neutral-500: oklch(0.50 0 0);
  --color-neutral-600: oklch(0.42 0 0);
  --color-neutral-700: oklch(0.32 0 0);
  --color-neutral-800: oklch(0.22 0 0);
  --color-neutral-900: oklch(0.13 0 0);
  --color-neutral-950: oklch(0.07 0 0);

  /* Typography - Brand Fonts */
  --font-title: "Bebas Neue", system-ui, sans-serif;
  --font-header: "Oswald", system-ui, sans-serif;
  --font-subheader: "Montserrat", system-ui, sans-serif;
  --font-display: "Bebas Neue", system-ui, sans-serif;
  --font-body: "Montserrat", system-ui, sans-serif;
  --font-mono: "JetBrains Mono", monospace;

  /* Spacing Scale */
  --spacing: 0.25rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-adventure: 0 4px 6px -1px oklch(0.15 0.12 142 / 0.1), 0 2px 4px -1px oklch(0.15 0.12 142 / 0.06);
  --shadow-mountain: 0 10px 15px -3px oklch(0.12 0.12 220 / 0.1), 0 4px 6px -2px oklch(0.12 0.12 220 / 0.05);
  --shadow-coffee: 0 20px 25px -5px oklch(0.10 0.08 45 / 0.1), 0 10px 10px -5px oklch(0.10 0.08 45 / 0.04);

  /* Animation Easings */
  --ease-adventure: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-mountain: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-coffee: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Breakpoints */
  --breakpoint-xs: 475px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Clean Components */
@layer components {
  .btn-primary {
    @apply inline-flex items-center justify-center px-8 py-3 text-sm font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply bg-primary-600 text-white border border-primary-600;
    font-family: var(--font-header);
  }

  .btn-primary:hover {
    @apply bg-primary-700 border-primary-700 text-white;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-8 py-3 text-sm font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply bg-transparent text-white border-2 border-orange-600;
    font-family: var(--font-header);
  }

  .btn-secondary:hover {
    @apply bg-orange-600 text-white;
  }

  .btn-large {
    @apply px-10 py-4 text-base;
  }

  .hero-gradient {
    @apply bg-gradient-to-br from-primary-900 to-primary-800;
  }

  .card-clean {
    @apply bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden transition-all duration-200;
  }

  .card-clean:hover {
    @apply shadow-md border-primary-200;
  }

  .text-gradient-primary {
    @apply bg-gradient-to-br from-primary-600 to-orange-600 bg-clip-text text-transparent;
  }

  /* Typography Classes */
  .title {
    font-family: var(--font-title);
    font-weight: 400;
    letter-spacing: 0.02em;
  }

  .header {
    font-family: var(--font-header);
    font-weight: 500;
  }

  .subheader {
    font-family: var(--font-subheader);
    font-weight: 500;
  }



  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-clean {
    @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Custom Animations */
@layer utilities {
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  .animate-fade-in {
    animation: fadeIn 1s ease-out forwards;
    opacity: 0;
  }

  .animate-scale-in {
    animation: scaleIn 0.6s ease-out forwards;
    opacity: 0;
    transform: scale(0.9);
  }

  .animate-page-enter {
    animation: pageEnter 0.6s ease-out forwards;
    opacity: 0;
  }

  .scale-102 {
    transform: scale(1.02);
  }

  /* Force hover utilities - Tailwind v4 compatibility */
  .hover\:bg-red-600:hover {
    background-color: #dc2626;
  }

  .hover\:bg-blue-600:hover {
    background-color: #2563eb;
  }

  .hover\:bg-red-500:hover {
    background-color: #ef4444;
  }

  .hover\:text-white:hover {
    color: #ffffff;
  }

  .hover\:border-red-500:hover {
    border-color: #ef4444;
  }

  .hover\:border-black\/40:hover {
    border-color: rgb(0 0 0 / 0.4);
  }

  .hover\:shadow-md:hover {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .hover\:shadow-xl:hover {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  .hover\:scale-105:hover {
    transform: scale(1.05);
  }

  .hover\:-translate-y-0\.5:hover {
    transform: translateY(-0.125rem);
  }

  .hover\:-translate-y-1:hover {
    transform: translateY(-0.25rem);
  }

  .hover\:-translate-y-2:hover {
    transform: translateY(-0.5rem);
  }

  /* Group hover effects */
  .group:hover .group-hover\:bg-\[\#2d4747\] {
    background-color: #2d4747;
  }

  .group:hover .group-hover\:border-\[\#2d4747\] {
    border-color: #2d4747;
  }

  .group:hover .group-hover\:bg-\[\#f5f4d1\] {
    background-color: #f5f4d1;
  }

  .group:hover .group-hover\:text-\[\#2d4747\] {
    color: #2d4747;
  }

  .group:hover .group-hover\:font-semibold {
    font-weight: 600;
  }

  .group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
  }

  .group:hover .group-hover\:brightness-110 {
    filter: brightness(1.1);
  }

  .group:hover .group-hover\:bg-\[\#1a1a1a\] {
    background-color: #1a1a1a;
  }

  .group:hover .group-hover\:bg-\[\#f5e633\] {
    background-color: #f5e633;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Global Styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  line-height: 1.6;
}

.scroll-mt-32 {
  scroll-margin-top: 8rem;
}

.svg-adventure {
  filter: drop-shadow(0 4px 6px oklch(0.15 0.12 142 / 0.1));
}

.svg-mountain {
  filter: drop-shadow(0 4px 6px oklch(0.12 0.12 220 / 0.1));
}
